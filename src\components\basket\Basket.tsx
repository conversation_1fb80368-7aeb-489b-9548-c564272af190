"use client";
import React, { useEffect, useState } from "react";
import EmptyBasket from "./EmptyBasket";
import ConfirmPayment from "./ConfirmPayment";
import EditBasketItem from "./EditBasketItem";
import NestedDrawer from "../ui/nested-drawer";
import CalendarCompaBasket from "./Calendar";
import { BasketItem } from "../../types/basket";
import type { CustomizationOption } from "../../types/basket"; // Import from types
import { Button } from "../ui/button";
import { Modal, ModalBody, ModalContent, ModalHeader } from "@heroui/react";
import { GetOrderDetailsByUserId, deleteOrder } from "@/services/ordersServices";
import { Timestamp } from "firebase/firestore";
import { formatDuration } from "@/services/serviceService";
import { Loader } from "react-feather";
import Link from "next/link";
import { getCurrencySymbol } from "@/services/currencyService"; // Import getCurrencySymbol function

interface BasketProps {
  items?: number[];
  userId: any;
  onOpenChange?: (open: boolean) => void;
  onDrawerChange?: (open: boolean) => void;
}

// Firebase Order interface with updated serviceDetails
interface FirebaseOrder {
  activityLog: { id: string }[];
  id: string;
  comment?: string;
  profileId?: string;
  selectedCustomizations?: string[];
  serviceId?: string;
  status?: string;
  userProfileId?: string;
  userProfileName?: string | null;
  added_at?: Timestamp;
  deleted?: boolean;
  specificDueDate?: any;
  currency?: string; // Add currency field from API
  userProfileDetails?: {
    id: string;
    email?: any;
    profile_name?: string;
  } | null;
  profileDetails?: {
    id: string;
    avatar?: string;
    profile_name?: string;
  } | null;
  serviceDetails?: {
    customizations: any[]; // Use any[] to avoid type conflicts, we'll transform it later
    id: string;
    title?: string;
    duration?: string;
    price?: string;
    description?: string;
  } | null;
  chargeId?: string;
}

const Basket: React.FC<BasketProps> = ({
  items = [1, 2, 3],
  userId,
  onOpenChange,
  onDrawerChange,
}) => {
  const [selectedItem, setSelectedItem] = useState<BasketItem | null>(null);
  const [orderToDelete, setOrderToDelete] = useState<string | null>(null);
  const [isConfirmPaymentOpen, setIsConfirmPaymentOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Function to format date to YYYY-MM-DD
  const formatDate = (date: Date) => {
    return date.toISOString().split("T")[0];
  };

  // Get yesterday's date as minimum allowed date
  const minDate = new Date();
  minDate.setDate(minDate.getDate() - 1);

  const [myOrders, setMyOrders] = useState<FirebaseOrder[]>([]);
  const [orderStates, setOrderStates] = useState<{
    [key: string]: {
      switchEnabled: boolean;
      selectedDate: Date;
      deliveryDate: string;
    };
  }>({});

  useEffect(() => {
    if (userId) {
      fetchOrders();
    }
  }, [userId]);

  const fetchOrders = async () => {
    setIsLoading(true);
    try {
      const resp = await GetOrderDetailsByUserId({ userId: userId, status: "BASKET" });
      if (resp.my_orders || resp.received_orders) {
        const orders = resp?.my_orders || [];
        // console.log(orders);

        setMyOrders(orders);

        // Initialize order states
        const initialStates: { [key: string]: any } = {};
        orders.forEach((order: FirebaseOrder) => {
          initialStates[order.id] = {
            switchEnabled: false,
            selectedDate: new Date(),
            deliveryDate: order.specificDueDate || "Flexible",
          };
        });
        setOrderStates(initialStates);
      }
    } catch (error) {
      console.log({ error });
    } finally {
      setIsLoading(false);
    }
  };

  // Add delete handler
  const handleDelete = async (orderId: string) => {
    try {
      setIsDeleting(true);
      const result = await deleteOrder(orderId);
      if (result.success) {
        setMyOrders((prevOrders) => prevOrders.filter((order) => order.id !== orderId));
        setIsDeleteModalOpen(false);
        setOrderToDelete(null);
      } else {
        console.error("Failed to delete order");
      }
    } catch (error) {
      console.error("Error deleting order:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleEditSave = async (updatedItem: BasketItem) => {
    const updatedItemWithDate = {
      ...updatedItem,
      orderDetails: {
        ...updatedItem.orderDetails,
        selectedDate: updatedItem.orderDetails.selectedDate || new Date(),
      },
    };
    // Refresh the orders data to reflect the changes
    await fetchOrders();
    // console.log("Saving updated item:", updatedItemWithDate);
  };

  const generateFileUrl = (postFile: string | undefined): string | undefined => {
    const baseUrl = process.env.BASE_STORAGE_URL;
    if (!baseUrl) return undefined;

    if (!postFile) {
      return undefined;
    }

    // Handle both dev/prod db urls
    if (postFile.startsWith("https://firebasestorage.googleapis.com/")) {
      return postFile;
    }

    return `${baseUrl}${encodeURIComponent(postFile)}?alt=media`;
  };

  const updateOrderState = (
    orderId: string,
    updates: Partial<{ switchEnabled: boolean; selectedDate: Date; deliveryDate: string }>
  ) => {
    setOrderStates((prev) => ({
      ...prev,
      [orderId]: {
        ...prev[orderId],
        ...updates,
      },
    }));
  };

  // Helper function to transform Firebase customization to BasketItem CustomizationOption
  const transformCustomizations = (firebaseCustomizations: any[]): CustomizationOption[] => {
    return firebaseCustomizations.map((customization) => ({
      id: customization.id,
      title: customization.title,
      description: customization.description,
      price: customization.price,
      duration: customization.duration,
      media: customization.media,
      updated_at: customization.updated_at,
    }));
  };

  // Helper function to create BasketItem from FirebaseOrder
  const createBasketItemFromOrder = (order: FirebaseOrder): BasketItem => {
    const orderState = orderStates[order.id] || {
      switchEnabled: false,
      selectedDate: new Date(),
      deliveryDate: "Flexible",
    };

    return {
      id: Number(order.id) || 0,
      orderId: order.id, // Store the original Firebase document ID
      title: order.serviceDetails?.title || "Untitled Service",
      description: order.serviceDetails?.description || "No description available",
      time: formatDuration(order.serviceDetails?.duration || "0"),
      subtotal: parseFloat(order.serviceDetails?.price || "0"),
      image: generateFileUrl(order.profileDetails?.avatar) || "",
      userName: order.profileDetails?.profile_name || "Unknown User",
      selectedCustomizations: order.selectedCustomizations || [],
      serviceDetails: order.serviceDetails
        ? {
            customizations: transformCustomizations(order.serviceDetails.customizations || []),
            id: order.serviceDetails.id,
            title: order.serviceDetails.title,
            duration: order.serviceDetails.duration,
            price: order.serviceDetails.price,
            description: order.serviceDetails.description,
          }
        : null,
      orderDetails: {
        deliveryDate: orderState.deliveryDate,
        switchEnabled: orderState.switchEnabled,
        transactionFee: 0,
        selectedDate: orderState.selectedDate,
        comment: order.comment,
        selectedOptions: order.selectedCustomizations || [],
      },
    };
  };

  // Helper to get currency symbol from an order
  const getOrderCurrencySymbol = (order: FirebaseOrder | undefined) => {
    if (
      order &&
      order.profileDetails &&
      typeof order.profileDetails === "object" &&
      "currency" in order.profileDetails &&
      typeof (order.profileDetails as any).currency === "string"
    ) {
      return getCurrencySymbol((order.profileDetails as any).currency);
    }
    return "£"; // Default to pound symbol
  };

  // Helper to get currency code from an order
  const getOrderCurrency = (order: FirebaseOrder | undefined) => {
    if (
      order &&
      order.profileDetails &&
      typeof order.profileDetails === "object" &&
      "currency" in order.profileDetails &&
      typeof (order.profileDetails as any).currency === "string"
    ) {
      return (order.profileDetails as any).currency;
    }
    return "gbp"; // Default to GBP
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-100px)]">
        <div className="flex flex-col items-center justify-center">
          <div className="relative">
            <Loader size={48} className="text-primary animate-spin" />
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-4 h-4 bg-white rounded-full"></div>
            </div>
          </div>
          <p className="text-sm text-gray-500">Loading Basket...</p>
        </div>
      </div>
    );
  }

  if (!myOrders || myOrders.length === 0) {
    return <EmptyBasket />;
  }

  console.log(myOrders);

  return (
    <>
      {myOrders.map((order) => {
        const orderState = orderStates[order.id] || {
          switchEnabled: false,
          selectedDate: new Date(),
          deliveryDate: "Flexible",
        };

        return (
          <div key={order.id} className="flex flex-col mb-8">
            <div className="row gap-3 mb-2">
              <img
                src={generateFileUrl(order?.profileDetails?.avatar) || "/assets/profileAvatar.svg"}
                alt={order?.profileDetails?.profile_name || "User avatar"}
                className="w-12 h-12 rounded-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = "/default-avatar.png";
                }}
              />
              <Link
                href={
                  order?.profileDetails?.profile_name
                    ? `/profile/amuzn/${order.profileDetails.profile_name.replace(/\s+/g, "-")}`
                    : "#"
                }
                className="text-xl"
                onClick={() => {
                  onOpenChange?.(false);
                  onDrawerChange?.(false);
                }}
              >
                {order?.profileDetails?.profile_name || "Unknown User"}
              </Link>
            </div>

            <div className="h-full">
              <div className="w-full mt-0 p-0 border-b-2 border-[#7C7C7C] pb-4">
                <div>
                  <Link
                    href={
                      order?.profileDetails?.profile_name
                        ? `/profile/amuzn/${order.profileDetails.profile_name.replace(/\s+/g, "-")}?view=Services`
                        : "#"
                    }
                    onClick={() => {
                      onOpenChange?.(false);
                      onDrawerChange?.(false);
                    }}
                  >
                    <p className="text-base font-bold text-primary my-2">
                      {order?.serviceDetails?.title || "Untitled Service"}
                    </p>
                  </Link>
                </div>
                <div className="flex justify-between">
                  <p className="text-subtitle">Approximate time</p>
                  <p className="text-lg font-bold text-subtitle">
                    {formatDuration(order?.serviceDetails?.duration || "0")}
                  </p>
                </div>
                <div className="flex justify-between">
                  <p className="text-subtitle">Service subtotal</p>
                  <p className="text-lg text-subtitle">
                    {getOrderCurrencySymbol(order)}
                    {parseFloat(order?.serviceDetails?.price || "0").toFixed(2)}
                  </p>
                </div>
                <div className="flex gap-3 mt-2">
                  <button
                    className="btn-xs border-primary btn w-full py-2 border rounded-full text-center"
                    onClick={() => {
                      const basketItem = createBasketItemFromOrder(order);
                      setSelectedItem(basketItem);
                      setIsEditOpen(true);
                    }}
                  >
                    Edit
                  </button>
                  <button
                    className="btn-xs border-primary btn w-full py-2 border rounded-full text-center"
                    onClick={() => {
                      setOrderToDelete(order.id);
                      setIsDeleteModalOpen(true);
                    }}
                  >
                    Delete
                  </button>
                </div>
              </div>

              {order?.selectedCustomizations?.map((customizationId) => {
                const customization = order?.serviceDetails?.customizations?.find(
                  (c) => c.id === customizationId
                );
                if (!customization) return null;

                return (
                  <div key={customizationId} className="flex justify-between">
                    {/* <p className="text-subtitle">{customization.title}</p>
                    <p className="text-lg font-bold text-subtitle">
                      {getOrderCurrencySymbol(order)}
                      {parseFloat(customization.price || "0").toFixed(2)}
                    </p> */}
                    <div className="w-full mt-0 p-0 border-b-2 border-[#7C7C7C] pb-4">
                      <div>
                        <Link
                          href={
                            order?.profileDetails?.profile_name
                              ? `/profile/amuzn/${order.profileDetails.profile_name.replace(/\s+/g, "-")}?view=Services`
                              : "#"
                          }
                          onClick={() => {
                            onOpenChange?.(false);
                            onDrawerChange?.(false);
                          }}
                        >
                          <p className="text-base font-bold text-primary my-2">
                            {order?.serviceDetails?.title || "Untitled Service"}
                          </p>
                        </Link>
                      </div>
                      <div className="flex justify-between">
                        <p className="text-subtitle">Approximate time</p>
                        <p className="text-lg font-bold text-subtitle">
                          {formatDuration(customization.duration || "0")}
                        </p>
                      </div>
                      <div className="flex justify-between">
                        <p className="text-subtitle">Service subtotal</p>
                        <p className="text-lg text-subtitle">
                          {getOrderCurrencySymbol(order)}
                          {parseFloat(customization.price || "0").toFixed(2)}
                        </p>
                      </div>
                      <div className="flex gap-3 mt-2">
                        <button
                          className="btn-xs border-primary btn w-full py-2 border rounded-full text-center"
                          onClick={() => {
                            const basketItem = createBasketItemFromOrder(order);
                            setSelectedItem(basketItem);
                            setIsEditOpen(true);
                          }}
                        >
                          Edit
                        </button>
                        <button
                          className="btn-xs border-primary btn w-full py-2 border rounded-full text-center"
                          onClick={() => {
                            setOrderToDelete(order.id);
                            setIsDeleteModalOpen(true);
                          }}
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })}

              <div className="mt-4 flex gap-3 items-center">
                <div className="flex-shrink-0 items-center justify-center">
                  <div className="relative -mt-[31px]">
                    <input
                      type="checkbox"
                      checked={orderState.switchEnabled}
                      onChange={(e) => {
                        updateOrderState(order.id, { switchEnabled: e.target.checked });
                      }}
                      className="sr-only"
                      id={`switch-${order.id}`}
                    />
                    <label htmlFor={`switch-${order.id}`} className="flex cursor-pointer">
                      <div
                        className={`block w-10 h-6 rounded-full transition-colors duration-200 ${orderState.switchEnabled ? "bg-primary" : "bg-gray-300"}`}
                      >
                        <div
                          className={`dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform duration-200 ${orderState.switchEnabled ? "transform translate-x-4" : ""}`}
                        />
                      </div>
                    </label>
                  </div>
                </div>
                <div className="flex-grow">
                  <div className="flex justify-between">
                    <p>Order subtotal</p>
                    <p>
                      {getOrderCurrencySymbol(order)}{" "}
                      {parseFloat(order.serviceDetails?.price || "0").toFixed(2)}
                    </p>
                  </div>
                  <p className="text-[#969696] text-sm">Excludes Transaction Fee (4%)</p>
                  <p className="text-primary font-bold pt-1 pb-2">
                    Request specific delivery due date
                  </p>

                  <p className="text-[#969696] text-sm">
                    Note: order due date is calculated based on the time required to deliver
                    specific service(s). Here you can request a specific delivery due date for this
                    order.
                  </p>
                </div>
              </div>

              {orderState.switchEnabled && (
                <>
                  <p className="text-titleLabel font-bold my-2">New Date</p>
                  <input
                    type="date"
                    className="w-full p-2 border rounded-md mb-2 bg-gray-50 cursor-not-allowed"
                    value={formatDate(orderState.selectedDate)}
                    min={formatDate(minDate)}
                    disabled
                  />
                  <div>
                    <CalendarCompaBasket
                      activeBorderColor="#a0a096"
                      activeDate={orderState.selectedDate}
                      minDate={minDate}
                      setActiveDate={(date: Date) => {
                        updateOrderState(order.id, {
                          selectedDate: date,
                          deliveryDate: formatDate(date),
                        });
                      }}
                    />
                  </div>
                </>
              )}

              <div className="mt-4 px-[2px]">
                <label
                  htmlFor={`message-${order.id}`}
                  className="text-titleLabel text-base font-bold mb-1 block"
                >
                  Order delivery details
                </label>
                <textarea
                  placeholder="Order delivery details"
                  id={`message-${order.id}`}
                  className="text-primary text-base h-32 w-full border rounded-md p-2 resize-none"
                  defaultValue={order.comment || ""}
                />
              </div>
              <button
                className="btn-xs text-white btn py-4 w-full bg-primary rounded-full mt-2"
                onClick={() => {
                  const basketItem = createBasketItemFromOrder(order);
                  console.log("Place Order clicked - Order data:", {
                    order,
                    profileDetails: order.profileDetails,
                    userProfileDetails: order.userProfileDetails,
                    basketItem,
                  });
                  setSelectedItem(basketItem);
                  setIsConfirmPaymentOpen(true);
                }}
              >
                Place Order
              </button>
            </div>
          </div>
        );
      })}

      {/* Delete Confirmation Modal */}
      <Modal
        placement="auto"
        onOpenChange={setIsDeleteModalOpen}
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setOrderToDelete(null);
        }}
        hideCloseButton={true}
      >
        <ModalContent className="modal-content w-80 p-12 rounded-3xl">
          {() => (
            <>
              <ModalBody>
                <p className="text-center text-black text-lg">
                  Are you sure you want to delete this service from the Basket?
                </p>
                <div>
                  <Button
                    variant="outline"
                    className="rounded-full w-full mt-5 border-black text-black border-2 py-5 text-base"
                    onClick={() => {
                      if (orderToDelete) {
                        handleDelete(orderToDelete);
                      }
                    }}
                    disabled={isDeleting}
                  >
                    {isDeleting ? (
                      <div className="flex items-center justify-center gap-2">
                        <Loader size={20} className="animate-spin" />
                        <span>Deleting...</span>
                      </div>
                    ) : (
                      "Yes, delete"
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    className="rounded-full w-full mt-3 border-black text-black border-2 py-5 text-base"
                    onClick={() => {
                      setIsDeleteModalOpen(false);
                      setOrderToDelete(null);
                    }}
                  >
                    No, cancel
                  </Button>
                </div>
              </ModalBody>
            </>
          )}
        </ModalContent>
      </Modal>

      <NestedDrawer
        isOpen={isConfirmPaymentOpen}
        onOpenChange={setIsConfirmPaymentOpen}
        title={`Confirm payment`}
      >
        <ConfirmPayment
          selectedItem={selectedItem}
          onConfirm={() => {
            setIsConfirmPaymentOpen(false);
          }}
          currencySymbol={getOrderCurrencySymbol(
            myOrders.find((o) => o.profileDetails?.profile_name === selectedItem?.userName)
          )}
          profileDetails={
            myOrders.find((o) => o.profileDetails?.profile_name === selectedItem?.userName)
              ?.profileDetails
          }
          userProfileDetails={
            myOrders.find((o) => o.profileDetails?.profile_name === selectedItem?.userName)
              ?.userProfileDetails
          }
        />
      </NestedDrawer>

      <NestedDrawer isOpen={isEditOpen} onOpenChange={setIsEditOpen} title={`Edit Service`}>
        <EditBasketItem
          selectedItem={selectedItem}
          onSave={handleEditSave}
          onClose={() => setIsEditOpen(false)}
          currencySymbol={getOrderCurrencySymbol(
            myOrders.find((o) => o.profileDetails?.profile_name === selectedItem?.userName)
          )}
        />
      </NestedDrawer>
    </>
  );
};

export default Basket;
