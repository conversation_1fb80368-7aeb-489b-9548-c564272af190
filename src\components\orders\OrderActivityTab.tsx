import React from "react";
import { Clock, DollarSign } from "react-feather";
import { format } from "date-fns";
import RichTextFormatter from "../RichTextFormatter";

interface TimelineItem {
  type: "order_status_update" | "payment";
  date: string;
  status: string;
  description?: string;
  message?: string;
  amountPercent?: number;
  recipient?: string;
}

interface Order {
  id: string | number;
  status: string;
  orderNumber: string;
  image: string;
  userName: string;
  title: string;
  totalCost: number;
  activityLog: any[];
}

interface OrderDetailsTabProps {
  order: Order;
  orderType: string;
}

const OrderActivityTab: React.FC<OrderDetailsTabProps> = ({ order, orderType }) => {
  // console.log(order.activityLog);

  const formatDate = (dateInput: any) => {
    let date: Date;
    if (
      dateInput &&
      typeof dateInput === "object" &&
      "seconds" in dateInput &&
      "nanoseconds" in dateInput
    ) {
      // Firestore Timestamp
      date = new Date(dateInput.seconds * 1000 + dateInput.nanoseconds / 1e6);
    } else {
      // ISO string or Date
      date = new Date(dateInput);
    }
    if (isNaN(date.getTime())) return "Invalid date";
    return format(date, "MMM d, yyyy h:mm a");
  };

  // Filter activity log based on order type
  const getFilteredActivityLog = () => {
    if (!Array.isArray(order.activityLog)) return [];

    if (orderType === "placed") {
      // For "placed" orders, show all "Order status update" items
      return order.activityLog.filter((item) => item.type === "Order status update");
    }

    // For other order types, show all activity log items
    return order.activityLog;
  };

  const filteredActivityLog = getFilteredActivityLog();

  console.log(order);
  console.log(orderType);
  console.log("Filtered activity log:", filteredActivityLog);

  return (
    <div className="space-y-4">
      {filteredActivityLog.length > 0 ? (
        filteredActivityLog.map((item, index) => (
          <div key={index} className="flex gap-3">
            <div className="flex-1">
              <div className="flex flex-row items-center justify-between gap-2 mb-1">
                <p className="text-sm text-primary max-md:text-xs">{item.type}</p>
                <span className="text-sm text-gray-500 text-nowrap max-md:text-xs">
                  {formatDate(item.date)}
                </span>
              </div>
              <p className="font-bold text-gray-900 text-sm ">{item.title}</p>

              <p className="text-sm text-gray-600 mb-1">
                <RichTextFormatter
                  text={item.description}
                  className="mt-2 text-subtitle"
                  preserveWhitespace={true}
                  enableMarkdown={true}
                />
              </p>
            </div>
          </div>
        ))
      ) : (
        <div className="py-8 text-center text-gray-500">
          {orderType === "placed"
            ? "No order status updates available for this order."
            : "No activity yet for this order."}
        </div>
      )}
    </div>
  );
};

export default OrderActivityTab;
