import React, { useState } from "react";
import NestedDrawer from "../ui/nested-drawer";
import * as Tabs from "@radix-ui/react-tabs";
import { TabsContent } from "@/components/ui/tabs";
import { ChevronLeft, MoreHorizontal, MoreVertical } from "react-feather";
import ChangeStatusDrawer from "./ChangeStatusDrawer";
import OrderDetailsTab from "./OrderDetailsTab";
import OrderActivityTab from "./OrderActivityTab";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Modal, ModalContent, ModalBody } from "@heroui/react";

interface Order {
  id: string | number;
  status: string;
  orderNumber: string;
  uniqueId: string;
  image: string;
  userName: string;
  title: string;
  totalCost: string;
  activityLog: any[];
  selectedCustomizations?: string[];
  serviceDetails?: {
    id: string;
    title?: string;
    duration?: number | string;
    price?: number;
    description?: string;
    customizations?: any[];
  };
  serviceModel: {
    price?: number;
  };
  comment?: string;
  deliveryDetails?: string;
}

interface OrderDetailProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  selectedOrderItem: string | number | null;
  order: any;
  currencySymbol?: string;
  currencyCode?: string;
  userId: string;
}

const OrderDetail: React.FC<OrderDetailProps> = ({
  isOpen,
  onOpenChange,
  selectedOrderItem,
  order,
  currencySymbol = "$",
  currencyCode = "USD",
  userId,
}) => {
  // console.log(order);

  // Get current user information from localStorage
  const getCurrentUserInfo = () => {
    try {
      const userStr = localStorage.getItem("user");
      if (userStr) {
        const userData = JSON.parse(userStr);
        return userData;
      }
    } catch (error) {
      console.error("Error parsing user from localStorage:", error);
    }
    return null;
  };

  const currentUser = getCurrentUserInfo();

  if (!order) return null;

  const [isChangeStatusOpen, setIsChangeStatusOpen] = useState(false);
  const [isContactSellerOpen, setIsContactSellerOpen] = useState(false);
  const [isRequestCancelOpen, setIsRequestCancelOpen] = useState(false);
  const [isContactSupportOpen, setIsContactSupportOpen] = useState(false);

  const handleStatusChange = (newStatus: string) => {
    // Here you would typically update the order status in your backend
    console.log("Changing status to:", newStatus);
  };

  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  return (
    <>
      <NestedDrawer
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        title={`Order #${order?.orderNumber || order.uniqueId || order.id || ""}`}
      >
        <div className="flex flex-col -mt-4">
          {/* <div className="flex items-center justify-between w-full px-4 py-2 border-b">
            <div className="flex items-center gap-3">
              <button onClick={() => onOpenChange(false)} className="p-1 hover:bg-gray-100 rounded-full">
                <ChevronLeft size={24} />
              </button>
              <h2 className="text-lg font-medium">Order #{order.orderNumber}</h2>
            </div>
            <button className="p-2 hover:bg-gray-100 rounded-full">
              <MoreVertical size={20} />
            </button>
          </div> */}

          <div className="px-4">
            <div className="flex items-center justify-between mb-6 mt-4">
              <div>
                <p className="text-sm text-gray-500">Current Status:</p>
                <p className="text-base font-semibold text-primary">{order.status}</p>
              </div>
              <div className="flex items-center gap-2">
                {order.status !== "COMPLETED" && (
                  <button
                    onClick={() => setIsChangeStatusOpen(true)}
                    className="px-4 py-1 text-sm bg-primary text-white rounded-full hover:bg-gray-800 transition-colors"
                  >
                    Change Status
                  </button>
                )}

                <div>
                  <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
                    <DropdownMenuTrigger asChild className="justify-end">
                      <MoreHorizontal className=" cursor-pointer" />
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-60 rounded-3xl z-50">
                      <DropdownMenuLabel
                        className="text-center font-normal text-base border-b-2 py-3 cursor-pointer"
                        onClick={() => {
                          window.location.href = "mailto:<EMAIL>"; // Replace with actual seller email if available
                        }}
                      >
                        Contact Seller
                      </DropdownMenuLabel>
                      {order.status !== "COMPLETED" && (
                        <DropdownMenuLabel
                          className="text-center font-normal text-base border-b-2 py-3 cursor-pointer"
                          onClick={() => {
                            setIsRequestCancelOpen(true);
                            setIsDropdownOpen(false);
                          }}
                        >
                          Request Cancellation
                        </DropdownMenuLabel>
                      )}
                      <DropdownMenuLabel
                        className="text-center font-normal text-base cursor-pointer"
                        onClick={() => {
                          window.location.href = "mailto:<EMAIL>"; // Replace with actual support email if available
                        }}
                      >
                        Contact Customer Support
                      </DropdownMenuLabel>

                      <DropdownMenuSeparator />
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </div>

            <Tabs.Root defaultValue="details" className="w-full">
              <div className="TabsListBg w-full flex rounded-md bg-none p-1">
                <Tabs.List className="w-full flex h-[30px]" aria-label="Order details">
                  <Tabs.Trigger
                    className="TabsTriggerBg flex-1 py-2 rounded-md data-[state=active]:bg-[#BDBDBD] data-[state=active]:text-primary"
                    value="details"
                  >
                    Details
                  </Tabs.Trigger>
                  <Tabs.Trigger
                    className="TabsTriggerBg flex-1 py-2 rounded-md data-[state=active]:bg-[#BDBDBD] data-[state=active]:text-primary"
                    value="activity"
                  >
                    Activity log
                  </Tabs.Trigger>
                </Tabs.List>
              </div>

              <div className="mt-6">
                <TabsContent value="details">
                  <OrderDetailsTab
                    order={order}
                    currencySymbol={currencySymbol}
                    currencyCode={currencyCode}
                  />
                </TabsContent>

                <TabsContent value="activity">
                  <OrderActivityTab
                    order={order}
                    orderType={order.userProfileId === userId ? "placed" : "received"}
                  />
                </TabsContent>
              </div>
            </Tabs.Root>
          </div>
        </div>
      </NestedDrawer>

      <ChangeStatusDrawer
        isOpen={isChangeStatusOpen}
        onOpenChange={setIsChangeStatusOpen}
        currentStatus={order.status}
        onStatusChange={handleStatusChange}
        orderType={order.userProfileId === userId ? "placed" : "received"}
        orderId={order.id}
        loggedInUser={currentUser?.profile_name || currentUser?.displayName || "Unknown User"}
        sellerName={order.profileDetails?.profile_name || "Unknown Seller"}
        userName={order.userProfileDetails?.profile_name || "Unknown User"}
      />

      {/* Modals for DropdownMenuLabel actions */}
      <Modal isOpen={isContactSellerOpen} onClose={() => setIsContactSellerOpen(false)}>
        <ModalContent className="modal-content w-80 p-8 rounded-3xl">
          <ModalBody>
            <p className="text-center text-black text-lg">Contact Seller Modal</p>
          </ModalBody>
        </ModalContent>
      </Modal>
      <Modal isOpen={isRequestCancelOpen} onClose={() => setIsRequestCancelOpen(false)}>
        <ModalContent className="modal-content w-80 p-8 rounded-3xl">
          <ModalBody>
            <p className="text-center text-black text-lg">Request Cancellation Modal</p>
          </ModalBody>
        </ModalContent>
      </Modal>
      <Modal isOpen={isContactSupportOpen} onClose={() => setIsContactSupportOpen(false)}>
        <ModalContent className="modal-content w-80 p-8 rounded-3xl">
          <ModalBody>
            <p className="text-center text-black text-lg">Contact Customer Support Modal</p>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};

export default OrderDetail;
